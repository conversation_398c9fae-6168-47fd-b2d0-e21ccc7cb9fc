import NoImage from '@/components/NoImage'
import { IS_INTERNAL_CONFIG } from '@/consts'
import { DATE_FORMAT_BASE } from '@/consts/date'
import { exportVenue } from '@/utils/export'
import { envUrl } from '@/utils/request'
import { DownloadOutlined, PlusOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { Button, Card, Divider, Flex, Image, List, Space, Tooltip, Typography, message } from 'antd'
import dayjs from 'dayjs'
import React from 'react'
import { IVenueListItem } from '../store'

// 场地列表
const VenueList: React.FC<any> = ({ data, pagination, loading, onChange, onOperate }) => {
  const handleCardClick = (venue: IVenueListItem, event: React.MouseEvent) => {
    if (event.target?.className?.indexOf?.('full-h') > -1 || event.target?.className?.indexOf?.('ant-flex') > -1) {
      onOperate && onOperate('view', venue)
    }
  }

  // 导出场地
  const handleExport = async () => {
    try {
      await exportVenue('场地列表.xlsx')
    } catch (error) {
      message.error('导出失败，请重试')
    }
  }

  // 渲染场地卡片
  const renderVenueCard = (venue: IVenueListItem) => {
    // 处理适合类型展示
    const suitableTypes = venue.suitableTypeList || (venue.suitableType ? venue.suitableType.split(',') : [])

    const urlImg = venue?.photos ? venue.photos.split(',')[0] : ''

    return (
      <List.Item>
        <Card hoverable size="small" className="full-h hover-move" onClick={e => handleCardClick(venue, e)}>
          <Flex gap={24}>
            <Image.PreviewGroup items={venue.photoList?.map(url => `${envUrl}${url}`)}>
              {urlImg ? (
                <Image
                  width={90}
                  height={120}
                  className="radius img-cover"
                  preview={{ getContainer: document.body }}
                  onClick={e => e.stopPropagation()}
                  src={`${urlImg ? envUrl + urlImg : ''}`}
                />
              ) : (
                <NoImage />
              )}
            </Image.PreviewGroup>

            <Flex vertical gap={16}>
              <Space size={2} split={<Divider type="vertical" />}>
                <Typography.Text strong className="fs-lg">
                  {venue.venueName}
                </Typography.Text>
                {!!venue.cost && (
                  <Tooltip title="拍摄费用">
                    <Typography.Text type={venue.cost ? 'danger' : 'warning'}>
                      {venue.cost ? `￥ ${venue.cost}` : '面议'}
                    </Typography.Text>
                  </Tooltip>
                )}
                {!!venue.cityName && <Typography.Text>{venue.cityName}</Typography.Text>}
                {typeof venue.isInternal === 'number'
                  ? `${IS_INTERNAL_CONFIG[venue.isInternal]?.label}场地` || '未知'
                  : null}
                {!!suitableTypes.length && <Dict title="适合" value={suitableTypes.join('、')} />}
                {venue.contactPerson && <Dict title="联系人" value={venue.contactPerson} />}
              </Space>
              {venue.address && (
                <Tooltip title="详细地址">
                  <Typography.Text>{venue.address}</Typography.Text>
                </Tooltip>
              )}
              <Space size={24}>
                {!venue.updateTime ? (
                  <Space>
                    {venue.creator && <Typography.Text>{venue.creator}</Typography.Text>}
                    <Dict
                      title="添加于"
                      value={
                        <Typography.Text type="secondary">
                          {dayjs(venue.createTime).format(DATE_FORMAT_BASE)}
                        </Typography.Text>
                      }
                    />
                  </Space>
                ) : (
                  <Space>
                    {venue.lastModifier && <Typography.Text>{venue.lastModifier}</Typography.Text>}
                    <Dict
                      title="最近更新于"
                      value={
                        <Typography.Text type="secondary">
                          {dayjs(venue.updateTime).format(DATE_FORMAT_BASE)}
                        </Typography.Text>
                      }
                    />
                  </Space>
                )}
              </Space>
            </Flex>
          </Flex>
        </Card>
      </List.Item>
    )
  }

  return (
    <Flex vertical>
      <ListHeader title="场地列表" total={pagination?.total} unitText="个">
        <Space>
          <Button icon={<DownloadOutlined />} onClick={handleExport}>
            导出
          </Button>
          <Button type="primary" ghost icon={<PlusOutlined />} onClick={() => onOperate && onOperate('create')}>
            添加场地
          </Button>
        </Space>
      </ListHeader>
      <List
        loading={loading}
        dataSource={data}
        renderItem={renderVenueCard}
        split={false}
        rowKey="id"
        className="list-sm"
        pagination={{
          ...pagination,
          onChange,
          onShowSizeChange: onChange,
        }}
      />
    </Flex>
  )
}

export default VenueList
