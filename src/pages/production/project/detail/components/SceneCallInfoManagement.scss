.draggableItem {
  margin-bottom: 8px;
  transition: all 0.2s ease;
  cursor: grab;

  &.dragging {
    opacity: 0.5;
    cursor: grabbing;
    transform: rotate(2deg);
    transition: none;

    .dragIcon {
      color: #1890ff;
    }
  }

  &.dropTarget {
    .dividerContainer {
      border: 2px solid #1890ff;
      background: #e6f7ff;
    }

    .sceneCard {
      border: 2px solid #1890ff;
      background: #e6f7ff;
    }
  }
}

.sceneCard {
  transition: all 0.2s ease;

  &.dragging {
    box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
  }

  &.dropTarget {
    border: 2px solid #1890ff;
    background: #e6f7ff;
  }
}

.sceneListContainer {
  overflow-y: auto;
  padding-right: 8px;
  height: calc(100vh - 150px);

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: #c1c1c1;

    &:hover {
      background: #a8a8a8;
    }
  }
}
