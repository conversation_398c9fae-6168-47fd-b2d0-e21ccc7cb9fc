import { <PERSON><PERSON>, Col, Drawer, Form, InputNumber, Radio, Row, Space, Switch, message } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import {
  BUDGET_GROUPS,
  BUDGET_SUBCATEGORY_CONFIG,
  BudgetCategory,
  BudgetSubcategory,
  getBudgetSubcategoryLabel,
} from '../../../../../consts/budget'
import { IProductionListItem } from '../../list/store'
import useProjectDetailStore, { IPrProductionBudgetItem } from '../store'

interface IExpenseFormItem {
  category: BudgetCategory
  subcategory: BudgetSubcategory
  quotedPrice?: number
  personCount?: number
  dayCount?: number
  totalPrice?: number
  hasInvoice: boolean
  // 预计算的显示数据
  subcategoryLabel: string
}

interface IAddNewBudgetExpensesProps {
  open: boolean
  onCancel: () => void
  onSuccess: () => void
  loading?: boolean
  productionId: number
  project?: IProductionListItem
  existingSubcategories?: Set<number> // 已存在的二级分类，用于防重复
}

const AddNewBudgetExpenses: React.FC<IAddNewBudgetExpensesProps> = ({
  open,
  onCancel,
  onSuccess,
  loading = false,
  productionId,
  project,
  existingSubcategories = new Set(),
}) => {
  const [form] = Form.useForm()
  const [activeTab, setActiveTab] = useState('production')
  const { saveBudgetItems } = useProjectDetailStore()

  // 存储每个 tab 的表单数据
  const [tabFormData, setTabFormData] = useState<Record<string, IExpenseFormItem[]>>({})

  // 预计算分组选项数据
  const groupOptions = useMemo(() => {
    return BUDGET_GROUPS.map(group => ({
      key: group.key,
      label: group.label,
      value: group.key,
    }))
  }, [])

  // 预计算分组费用数据（过滤已存在的subcategory）
  const groupedExpenseData = useMemo(() => {
    const groupedList: Record<string, IExpenseFormItem[]> = {}

    BUDGET_GROUPS.forEach(group => {
      // 过滤掉已存在的subcategory
      const availableSubcategories = group.subcategories.filter(
        subcategory => !existingSubcategories.has(subcategory)
      )

      groupedList[group.key] = availableSubcategories.map(subcategory => {
        const subcategoryConfig = BUDGET_SUBCATEGORY_CONFIG[subcategory]
        return {
          category: subcategoryConfig.category,
          subcategory: subcategory,
          quotedPrice: undefined,
          personCount: undefined,
          dayCount: undefined,
          totalPrice: undefined,
          hasInvoice: false,
          subcategoryLabel: getBudgetSubcategoryLabel(subcategory),
        }
      })
    })

    return groupedList
  }, [existingSubcategories])

  // 处理模态框打开/关闭
  useEffect(() => {
    if (open) {
      form.resetFields()
      setActiveTab('production')
      // 初始化所有 tab 的数据
      const initialTabData: Record<string, IExpenseFormItem[]> = {}
      BUDGET_GROUPS.forEach(group => {
        initialTabData[group.key] = groupedExpenseData[group.key] || []
      })
      setTabFormData(initialTabData)

      // 延迟设置表单值，确保表单已经渲染
      setTimeout(() => {
        form.setFieldsValue({ expenseList: initialTabData['production'] || [] })
      }, 50)
    } else {
      form.setFieldsValue({ expenseList: [] })
      setTabFormData({})
    }
  }, [open, form, groupedExpenseData])

  // 保存当前 tab 的表单数据
  const saveCurrentTabData = () => {
    const currentFormData = form.getFieldValue('expenseList') || []
    setTabFormData(prev => ({
      ...prev,
      [activeTab]: currentFormData
    }))
  }

  // 处理 Tab 切换
  const handleTabChange = (e: any) => {
    if (e?.target?.value) {
      const newTab = e.target.value

      // 保存当前 tab 的数据
      saveCurrentTabData()

      // 切换到新 tab
      setActiveTab(newTab)

      // 恢复新 tab 的数据，如果没有则使用初始数据
      const newTabData = tabFormData[newTab] || groupedExpenseData[newTab] || []
      form.setFieldsValue({ expenseList: newTabData })
    }
  }



  // 处理保存
  const handleSave = async () => {
    try {
      // 保存当前 tab 的数据
      saveCurrentTabData()

      // 收集所有 tab 的数据
      const currentFormData = form.getFieldValue('expenseList') || []
      const allTabData = {
        ...tabFormData,
        [activeTab]: currentFormData
      }

      // 合并所有 tab 的费用项
      const allExpenses: IExpenseFormItem[] = []
      Object.values(allTabData).forEach(tabData => {
        if (Array.isArray(tabData)) {
          allExpenses.push(...tabData)
        }
      })

      if (allExpenses.length === 0) {
        message.warning('请至少添加一个费用项')
        return
      }

      // 过滤出有效的费用记录（有总价的）
      const validExpenses = allExpenses.filter((expense: IExpenseFormItem) =>
        expense.totalPrice && expense.totalPrice > 0
      )

      if (validExpenses.length === 0) {
        message.warning('请至少填写一个费用项的总价')
        return
      }

      // 验证是否有重复的二级分类
      const subcategories = validExpenses.map((expense: IExpenseFormItem) => expense.subcategory)
      const duplicateSubcategories = subcategories.filter((subcategory: number, index: number) =>
        subcategories.indexOf(subcategory) !== index
      )
      if (duplicateSubcategories.length > 0) {
        message.error('不能添加重复的二级分类')
        return
      }

      // 验证是否与已存在的二级分类重复
      const conflictSubcategories = subcategories.filter((subcategory: number) =>
        existingSubcategories.has(subcategory)
      )
      if (conflictSubcategories.length > 0) {
        message.error('选择的二级分类已存在，请选择其他分类')
        return
      }

      const budgetItems: IPrProductionBudgetItem[] = validExpenses.map((expense: IExpenseFormItem) => ({
        productionId,
        isPackage: false,
        quotedPrice: expense.quotedPrice || 0,
        personCount: expense.personCount || 0,
        dayCount: expense.dayCount || 0,
        totalPrice: expense.totalPrice,
        hasInvoice: expense.hasInvoice ? 1 : 0,
        itemsDetail: [
          {
            itemId: 0, // 新增时为0
            category: expense.category,
            subcategory: expense.subcategory,
            personCount: expense.personCount || 0,
            isMain: false
          },
        ],
      }))

      const success = await saveBudgetItems({
        productionId,
        budgetItems,
      })

      if (success) {
        message.success(`成功添加 ${validExpenses.length} 个费用项`)
        onSuccess()
        onCancel()
      } else {
        message.error('添加费用项失败')
      }
    } catch (error) {
      console.error('保存费用项失败:', error)
      message.error('添加费用项失败')
    }
  }

  return (
    <Drawer
      title="批量添加费用项"
      open={open}
      onClose={onCancel}
      width={1200}
      extra={
        <Space>
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" loading={loading} onClick={() => form.submit()}>
            保存
          </Button>
        </Space>
      }>
      <Form form={form} layout="vertical" onFinish={handleSave}>
        {/* Tab 切换 */}
        <Form.Item label="选择费用分组">
          <Radio.Group value={activeTab} onChange={handleTabChange}>
            <Row gutter={[16, 16]}>
              {groupOptions.map(group => (
                <Col span={6} key={group.key}>
                  <Radio.Button value={group.value} style={{ width: '100%', textAlign: 'center' }}>
                    {group.label}
                  </Radio.Button>
                </Col>
              ))}
            </Row>
          </Radio.Group>
        </Form.Item>

        {/* 费用项列表 */}
        <Form.List name="expenseList">
          {(fields) => (
            <>
              {fields.map(({ key, name, ...restField }) => {
                const currentExpense = form.getFieldValue(['expenseList', name])
                const subcategoryConfig = currentExpense?.subcategory ? BUDGET_SUBCATEGORY_CONFIG[currentExpense.subcategory as BudgetSubcategory] : null

                return (
                  <Row key={key} gutter={16} style={{ marginBottom: 16, padding: 16, border: '1px solid #f0f0f0', borderRadius: 6 }}>
                    <Col span={5}>
                      <Form.Item
                        {...restField}
                        label="费用项目"
                        style={{ marginBottom: 0 }}>
                        <div style={{ fontWeight: 500 }}>
                          {subcategoryConfig ? getBudgetSubcategoryLabel(currentExpense.subcategory) : '-'}
                        </div>
                      </Form.Item>
                    </Col>
                    <Col span={4}>
                      <Form.Item
                        {...restField}
                        name={[name, 'quotedPrice']}
                        label={`单价 (${project?.currencySymbol || '¥'})`}
                        style={{ marginBottom: 0 }}>
                        <InputNumber
                          min={0}
                          precision={2}
                          placeholder="单价"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={3}>
                      <Form.Item
                        {...restField}
                        name={[name, 'personCount']}
                        label="人数"
                        style={{ marginBottom: 0 }}>
                        <InputNumber
                          min={1}
                          placeholder="人数"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={3}>
                      <Form.Item
                        {...restField}
                        name={[name, 'dayCount']}
                        label="天数"
                        style={{ marginBottom: 0 }}>
                        <InputNumber
                          min={1}
                          placeholder="天数"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={4}>
                      <Form.Item
                        {...restField}
                        name={[name, 'totalPrice']}
                        label={`总价 (${project?.currencySymbol || '¥'})`}

                        style={{ marginBottom: 0 }}>
                        <InputNumber
                          min={0}
                          precision={2}
                          placeholder="请输入总价"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={3}>
                      <Form.Item
                        {...restField}
                        name={[name, 'hasInvoice']}
                        label="有发票"
                        valuePropName="checked"
                        style={{ marginBottom: 0 }}>
                        <Switch />
                      </Form.Item>
                    </Col>
                  </Row>
                )
              })}
            </>
          )}
        </Form.List>
      </Form>
    </Drawer>
  )
}

export default AddNewBudgetExpenses
